package com.studentmanagement.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.studentmanagement.dto.LoginRequest;
import com.studentmanagement.dto.LoginResponse;
import com.studentmanagement.dto.RegisterRequest;
import com.studentmanagement.entity.Result;
import com.studentmanagement.service.SysUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 登录控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
public class LoginController {

    @Resource
    private SysUserService sysUserService;

    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@RequestBody LoginRequest loginRequest) {
        try {
            log.info("用户登录请求: {}", loginRequest.getUsername());
            LoginResponse response = sysUserService.login(loginRequest);
            log.info("用户 {} 登录成功", loginRequest.getUsername());
            return Result.success(response);
        } catch (Exception e) {
            log.error("用户 {} 登录失败: {}", loginRequest.getUsername(), e.getMessage());
            return Result.failure(401, e.getMessage());
        }
    }

    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result<String> register(@RequestBody RegisterRequest registerRequest) {
        try {
            log.info("用户注册请求: {}", registerRequest.getUsername());
            boolean success = sysUserService.register(registerRequest);
            if (success) {
                log.info("用户 {} 注册成功", registerRequest.getUsername());
                return Result.success("注册成功");
            } else {
                log.error("用户 {} 注册失败", registerRequest.getUsername());
                return Result.failure("注册失败");
            }
        } catch (Exception e) {
            log.error("用户 {} 注册失败: {}", registerRequest.getUsername(), e.getMessage());
            return Result.failure(400, e.getMessage());
        }
    }

    /**
     * 用户登出
     * @return 登出结果
     */
    @PostMapping("/logout")
    public Result<String> logout() {
        try {
            Object loginId = StpUtil.getLoginIdDefaultNull();
            if (loginId != null) {
                log.info("用户 {} 登出", loginId);
                sysUserService.logout();
                return Result.success("登出成功");
            } else {
                return Result.failure("用户未登录");
            }
        } catch (Exception e) {
            log.error("登出失败: {}", e.getMessage());
            return Result.failure("登出失败");
        }
    }

    /**
     * 获取当前登录用户信息
     * @return 用户信息
     */
    @GetMapping("/userinfo")
    public Result<LoginResponse> getUserInfo() {
        try {
            // 检查是否登录
            if (!StpUtil.isLogin()) {
                return Result.failure(401, "用户未登录");
            }

            // 获取当前登录用户ID
            Long userId = StpUtil.getLoginIdAsLong();

            // 根据用户ID获取用户信息
            var user = sysUserService.getById(userId);
            if (user == null) {
                return Result.failure("用户不存在");
            }

            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setId(user.getId());
            response.setUsername(user.getUsername());
            response.setName(user.getName());
            response.setRole(user.getRole());
            response.setToken(StpUtil.getTokenValue());

            return Result.success(response);
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage());
            return Result.failure("获取用户信息失败");
        }
    }

    /**
     * 检查登录状态
     * @return 登录状态
     */
    @GetMapping("/check")
    public Result<Boolean> checkLogin() {
        boolean isLogin = StpUtil.isLogin();
        return Result.success(isLogin);
    }

}
