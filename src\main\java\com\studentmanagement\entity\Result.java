package com.studentmanagement.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Result<T> {
    /**
     * 状态码，用于标识请求的成功或失败
     */
    private int code;

    /**
     * 消息，用于描述请求的结果
     */
    private String message;

    /**
     * 数据，用于返回具体的业务数据
     */
    private T data;

    // 静态方法，用于快速创建成功或失败的响应对象

    /**
     * 创建一个成功的响应
     * @param data 返回的数据
     * @param <T> 泛型参数类型
     * @return Result对象
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(200, "操作成功", data);
    }

    /**
     * 创建一个成功的响应（无数据）
     * @param <T> 泛型参数类型
     * @return Result对象
     */
    public static <T> Result<T> success() {
        return new Result<>(200, "操作成功", null);
    }

    /**
     * 创建一个失败的响应
     * @param code 状态码
     * @param message 错误消息
     * @param <T> 泛型参数类型
     * @return Result对象
     */
    public static <T> Result<T> failure(int code, String message) {
        return new Result<>(code, message, null);
    }

    /**
     * 创建一个失败的响应（默认状态码500）
     * @param message 错误消息
     * @param <T> 泛型参数类型
     * @return Result对象
     */
    public static <T> Result<T> failure(String message) {
        return new Result<>(500, message, null);
    }
}

