<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.studentmanagement.mapper.StudentsMapper">

    <resultMap id="BaseResultMap" type="com.studentmanagement.entity.Students">
            <result property="studentId" column="student_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="age" column="age" jdbcType="INTEGER"/>
            <result property="major" column="major" jdbcType="VARCHAR"/>
            <result property="score" column="score" jdbcType="INTEGER"/>
            <result property="isActive" column="is_active" jdbcType="TINYINT"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
            <result property="createdByTx" column="created_by_tx" jdbcType="VARCHAR"/>
            <result property="updatedByTx" column="updated_by_tx" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        student_id,name,age,
        major,score,is_active,
        created_at,updated_at,created_by_tx,
        updated_by_tx
    </sql>
</mapper>
