# 学生管理系统 - 登录注册API测试文档

## 基础信息
- 服务器地址: http://localhost:8080
- 所有接口都在 `/api/auth` 路径下

## 1. 用户注册

### 接口信息
- **URL**: `POST /api/auth/register`
- **Content-Type**: `application/json`

### 请求参数
```json
{
    "username": "student001",
    "password": "123456",
    "name": "张三",
    "role": "STUDENT"
}
```

### 参数说明
- `username`: 用户名（必填，唯一）
- `password`: 密码（必填）
- `name`: 姓名（必填）
- `role`: 角色（必填，可选值：STUDENT、TEACHER、ADMIN）

### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": "注册成功"
}
```

### cURL 测试命令
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "student001",
    "password": "123456",
    "name": "张三",
    "role": "STUDENT"
  }'
```

## 2. 用户登录

### 接口信息
- **URL**: `POST /api/auth/login`
- **Content-Type**: `application/json`

### 请求参数
```json
{
    "username": "student001",
    "password": "123456"
}
```

### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "username": "student001",
        "name": "张三",
        "role": "STUDENT",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
}
```

### cURL 测试命令
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "student001",
    "password": "123456"
  }'
```

## 3. 获取用户信息

### 接口信息
- **URL**: `GET /api/auth/userinfo`
- **需要登录**: 是

### 请求头
```
token: your_token_here
```

### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "username": "student001",
        "name": "张三",
        "role": "STUDENT",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
}
```

### cURL 测试命令
```bash
curl -X GET http://localhost:8080/api/auth/userinfo \
  -H "token: your_token_here"
```

## 4. 检查登录状态

### 接口信息
- **URL**: `GET /api/auth/check`

### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": true
}
```

### cURL 测试命令
```bash
curl -X GET http://localhost:8080/api/auth/check \
  -H "token: your_token_here"
```

## 5. 用户登出

### 接口信息
- **URL**: `POST /api/auth/logout`
- **需要登录**: 是

### 请求头
```
token: your_token_here
```

### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": "登出成功"
}
```

### cURL 测试命令
```bash
curl -X POST http://localhost:8080/api/auth/logout \
  -H "token: your_token_here"
```

## 错误响应示例

### 用户名已存在
```json
{
    "code": 400,
    "message": "用户名已存在",
    "data": null
}
```

### 密码错误
```json
{
    "code": 401,
    "message": "密码错误",
    "data": null
}
```

### 用户未登录
```json
{
    "code": 401,
    "message": "用户未登录",
    "data": null
}
```

## 测试流程建议

1. 首先注册一个新用户
2. 使用注册的用户名和密码登录
3. 保存返回的token
4. 使用token访问需要登录的接口
5. 测试登出功能

## 注意事项

1. token需要在请求头中传递，字段名为 `token`
2. 角色只能是 STUDENT、TEACHER、ADMIN 中的一个
3. 用户名必须唯一
4. 密码会自动加密存储
5. token有效期为30天（可在配置文件中修改）
