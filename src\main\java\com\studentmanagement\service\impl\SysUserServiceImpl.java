package com.studentmanagement.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.studentmanagement.dto.LoginRequest;
import com.studentmanagement.dto.LoginResponse;
import com.studentmanagement.dto.RegisterRequest;
import com.studentmanagement.entity.SysUser;
import com.studentmanagement.service.SysUserService;
import com.studentmanagement.mapper.SysUserMapper;
import com.studentmanagement.util.PasswordUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
* <AUTHOR>
* @description 针对表【sys_user】的数据库操作Service实现
* @createDate 2025-07-24 17:34:19
*/
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser>
    implements SysUserService{

    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        // 参数校验
        if (!StringUtils.hasText(loginRequest.getUsername()) || !StringUtils.hasText(loginRequest.getPassword())) {
            throw new RuntimeException("用户名和密码不能为空");
        }

        // 根据用户名查找用户
        SysUser user = findByUsername(loginRequest.getUsername());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证密码
        if (!PasswordUtil.verifyPassword(loginRequest.getPassword(), user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        // 登录成功，生成token
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();

        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setId(user.getId());
        response.setUsername(user.getUsername());
        response.setName(user.getName());
        response.setRole(user.getRole());
        response.setToken(token);

        return response;
    }

    @Override
    public boolean register(RegisterRequest registerRequest) {
        // 参数校验
        if (!StringUtils.hasText(registerRequest.getUsername()) ||
            !StringUtils.hasText(registerRequest.getPassword()) ||
            !StringUtils.hasText(registerRequest.getName()) ||
            !StringUtils.hasText(registerRequest.getRole())) {
            throw new RuntimeException("注册信息不完整");
        }

        // 检查用户名是否已存在
        if (findByUsername(registerRequest.getUsername()) != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 验证角色是否合法
        if (!isValidRole(registerRequest.getRole())) {
            throw new RuntimeException("角色不合法，只能是STUDENT、TEACHER或ADMIN");
        }

        // 创建新用户
        SysUser user = new SysUser();
        user.setUsername(registerRequest.getUsername());
        user.setPassword(PasswordUtil.encryptPassword(registerRequest.getPassword()));
        user.setName(registerRequest.getName());
        user.setRole(registerRequest.getRole());

        return save(user);
    }

    @Override
    public SysUser findByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }

        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        return getOne(queryWrapper);
    }

    @Override
    public void logout() {
        StpUtil.logout();
    }

    /**
     * 验证角色是否合法
     * @param role 角色
     * @return 是否合法
     */
    private boolean isValidRole(String role) {
        return "STUDENT".equals(role) || "TEACHER".equals(role) || "ADMIN".equals(role);
    }

}




