package com.studentmanagement.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 密码加密工具类
 */
public class PasswordUtil {
    
    private static final String ALGORITHM = "SHA-256";
    private static final int SALT_LENGTH = 16;
    
    /**
     * 生成随机盐值
     * @return 盐值
     */
    private static String generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[SALT_LENGTH];
        random.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }
    
    /**
     * 使用SHA-256和盐值加密密码
     * @param password 原始密码
     * @param salt 盐值
     * @return 加密后的密码
     */
    private static String hashPassword(String password, String salt) {
        try {
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            md.update(salt.getBytes());
            byte[] hashedPassword = md.digest(password.getBytes());
            return Base64.getEncoder().encodeToString(hashedPassword);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }
    
    /**
     * 加密密码（生成盐值并加密）
     * @param password 原始密码
     * @return 格式为 "盐值:加密密码" 的字符串
     */
    public static String encryptPassword(String password) {
        String salt = generateSalt();
        String hashedPassword = hashPassword(password, salt);
        return salt + ":" + hashedPassword;
    }
    
    /**
     * 验证密码
     * @param password 原始密码
     * @param encryptedPassword 加密后的密码（格式为 "盐值:加密密码"）
     * @return 密码是否匹配
     */
    public static boolean verifyPassword(String password, String encryptedPassword) {
        if (encryptedPassword == null || !encryptedPassword.contains(":")) {
            return false;
        }
        
        String[] parts = encryptedPassword.split(":", 2);
        String salt = parts[0];
        String hashedPassword = parts[1];
        
        String newHashedPassword = hashPassword(password, salt);
        return hashedPassword.equals(newHashedPassword);
    }
}
