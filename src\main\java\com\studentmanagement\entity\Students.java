package com.studentmanagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName students
 */
@TableName(value ="students")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Students implements Serializable {
    /**
     * 学号，主键
     */
    // 使数据库id为自增
    @TableId(type = IdType.AUTO)
    private Long studentId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 专业
     */
    private String major;

    /**
     * 成绩(0-100)
     */
    private Integer score;

    /**
     * 是否有效(1:有效,0:已删除)
     */
    private Integer isActive;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 创建交易哈希
     */
    private String createdByTx;

    /**
     * 最后更新交易哈希
     */
    private String updatedByTx;

}