package com.studentmanagement.service;

import com.studentmanagement.dto.LoginRequest;
import com.studentmanagement.dto.LoginResponse;
import com.studentmanagement.dto.RegisterRequest;
import com.studentmanagement.entity.SysUser;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【sys_user】的数据库操作Service
* @createDate 2025-07-24 17:34:19
*/
public interface SysUserService extends IService<SysUser> {

    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 是否注册成功
     */
    boolean register(RegisterRequest registerRequest);

    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户信息
     */
    SysUser findByUsername(String username);

    /**
     * 用户登出
     */
    void logout();

}
