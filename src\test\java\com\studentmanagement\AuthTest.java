package com.studentmanagement;

import com.studentmanagement.dto.LoginRequest;
import com.studentmanagement.dto.RegisterRequest;
import com.studentmanagement.service.SysUserService;
import com.studentmanagement.util.PasswordUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import jakarta.annotation.Resource;

@SpringBootTest
@SpringJUnitConfig
public class AuthTest {

    @Resource
    private SysUserService sysUserService;

    @Test
    public void testPasswordUtil() {
        String password = "123456";
        String encrypted = PasswordUtil.encryptPassword(password);
        System.out.println("加密后的密码: " + encrypted);
        
        boolean isValid = PasswordUtil.verifyPassword(password, encrypted);
        System.out.println("密码验证结果: " + isValid);
        
        boolean isInvalid = PasswordUtil.verifyPassword("wrong", encrypted);
        System.out.println("错误密码验证结果: " + isInvalid);
    }

    @Test
    public void testRegisterAndLogin() {
        try {
            // 测试注册
            RegisterRequest registerRequest = new RegisterRequest();
            registerRequest.setUsername("testuser");
            registerRequest.setPassword("123456");
            registerRequest.setName("测试用户");
            registerRequest.setRole("STUDENT");
            
            boolean registerResult = sysUserService.register(registerRequest);
            System.out.println("注册结果: " + registerResult);
            
            // 测试登录
            LoginRequest loginRequest = new LoginRequest();
            loginRequest.setUsername("testuser");
            loginRequest.setPassword("123456");
            
            var loginResponse = sysUserService.login(loginRequest);
            System.out.println("登录结果: " + loginResponse);
            
        } catch (Exception e) {
            System.out.println("测试异常: " + e.getMessage());
        }
    }
}
