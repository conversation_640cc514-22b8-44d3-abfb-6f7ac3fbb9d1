package com.studentmanagement.controller;

import com.studentmanagement.entity.Result;
import com.studentmanagement.entity.Students;
import com.studentmanagement.service.StudentsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api")
public class StudentsController {

    @Resource
    private StudentsService studentsService;

    @GetMapping("/students")
    public Result<List<Students>> getStudents() {
        log.info("查询所有学生信息");
        return Result.success(studentsService.list());
    }

    @PostMapping("/students")
    public Result saveOrUpdateStudents(@RequestBody Students students) {
        log.info("保存或者修改学生信息{}",students);
        studentsService.saveOrUpdate(students);
        return Result.success();
    }

    @DeleteMapping("/students/{studentId}")
    public Result deleteStudents(@PathVariable Long studentId) {
        log.info("删除学生信息{}",studentId);
        studentsService.removeById(studentId);
        return Result.success();
    }
}
