#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学生管理系统API测试脚本
"""

import requests
import json

BASE_URL = "http://localhost:8080/api/auth"

def test_register():
    """测试用户注册"""
    print("=== 测试用户注册 ===")
    url = f"{BASE_URL}/register"
    data = {
        "username": "testuser001",
        "password": "123456",
        "name": "测试用户",
        "role": "STUDENT"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"注册失败: {e}")
        return False

def test_login():
    """测试用户登录"""
    print("\n=== 测试用户登录 ===")
    url = f"{BASE_URL}/login"
    data = {
        "username": "testuser001",
        "password": "123456"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {result}")
        
        if response.status_code == 200 and result.get('data'):
            token = result['data'].get('token')
            print(f"获取到token: {token[:50]}...")
            return token
        return None
    except Exception as e:
        print(f"登录失败: {e}")
        return None

def test_userinfo(token):
    """测试获取用户信息"""
    print("\n=== 测试获取用户信息 ===")
    url = f"{BASE_URL}/userinfo"
    headers = {"token": token}
    
    try:
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取用户信息失败: {e}")
        return False

def test_check_login(token):
    """测试检查登录状态"""
    print("\n=== 测试检查登录状态 ===")
    url = f"{BASE_URL}/check"
    headers = {"token": token}
    
    try:
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"检查登录状态失败: {e}")
        return False

def test_logout(token):
    """测试用户登出"""
    print("\n=== 测试用户登出 ===")
    url = f"{BASE_URL}/logout"
    headers = {"token": token}
    
    try:
        response = requests.post(url, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"登出失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试学生管理系统登录注册功能...")
    print("请确保应用程序已启动在 http://localhost:8080")
    
    # 测试注册
    if not test_register():
        print("注册测试失败，可能用户已存在，继续测试登录...")
    
    # 测试登录
    token = test_login()
    if not token:
        print("登录测试失败，停止后续测试")
        return
    
    # 测试获取用户信息
    test_userinfo(token)
    
    # 测试检查登录状态
    test_check_login(token)
    
    # 测试登出
    test_logout(token)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
